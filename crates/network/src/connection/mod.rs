#[cfg(test)]
mod tests;

use std::{net::Ipv4Addr, ops::Add, time::Duration};

use tokio::time::sleep;
use tracing::{error, info, info_span, warn, Instrument};

use crate::{
    configuration::NetworkConfiguration,
    credentials::Credentials,
    datacenter::{parse_datacenter_configuration, DatacenterConfiguration},
    errors::NetworkError,
    ports::{HalPort, PingerPort, RandomGeneratorPort},
    watchdog::{TaskHealthMonitor, TaskId},
};

#[derive(Debug)]
pub struct ConnectionHandler<PING: PingerPort, RANDOM: RandomGeneratorPort, HAL: HalPort> {
    pinger: PING,
    random_generator: RANDOM,
    hal: HAL,
    credentials: Credentials,
    conf: NetworkConfiguration,
    health_monitor: Option<TaskHealthMonitor>,
}

impl<PING: PingerPort, RANDOM: RandomGeneratorPort, HAL: HalPort>
    ConnectionHandler<PING, RANDOM, HAL>
{
    pub fn new(
        pinger: PING,
        random_generator: RANDOM,
        hal: HAL,
        credentials: Credentials,
        conf: NetworkConfiguration,
        health_monitor: Option<TaskHealthMonitor>,
    ) -> Self {
        Self {
            pinger,
            random_generator,
            hal,
            credentials,
            conf,
            health_monitor,
        }
    }

    pub async fn watch(&self) {
        info!("début de la supervision de la connexion");
        loop {
            // Send health heartbeat at the beginning of each monitoring cycle
            if let Some(health_monitor) = &self.health_monitor {
                health_monitor
                    .heartbeat(&TaskId::new("network_manager"))
                    .await;
            }

            if !self.can_modem_reach_si().await {
                warn!("le modem ne peut pas joindre le si. reboot du bip en cours.");
                let _ = self.hal.reboot_bip().await;
            };

            // Send health heartbeat after monitoring cycle
            if let Some(health_monitor) = &self.health_monitor {
                health_monitor
                    .heartbeat(&TaskId::new("network_manager"))
                    .await;
            }

            sleep(self.conf.check_interval).await;
        }
    }

    async fn can_modem_reach_si(&self) -> bool {
        let Some(datacenter_conf) = parse_datacenter_configuration(&self.conf) else {
            error!("échec lors de l'obtention du datacenter principal");
            return false;
        };

        let mut successive_modem_reboots: u8 = 0;
        while successive_modem_reboots < self.conf.modem_resets_before_bip_reboot {
            if self.is_si_reachable(&datacenter_conf).await {
                return true;
            }
            warn!("si injoignable");
            info!(
                "reset modem en cours ({}, {})",
                successive_modem_reboots, self.conf.modem_resets_before_bip_reboot
            );
            let _ = self.restart_modem().await;
            successive_modem_reboots += 1;
            sleep(self.conf.waiting_delay_after_modem_reset).await;

            info!("reconnexion du boitier en cours");
            match self
                .hal
                .connect(
                    &self.credentials.apn,
                    &self.credentials.login,
                    &self.credentials.password,
                )
                .await
            {
                Ok(_) => info!("reconnexion réussie !"),
                Err(_) => warn!("échec de la reconnexion"),
            }
        }

        return false;
    }

    async fn is_si_reachable(&self, datacenter_configuration: &DatacenterConfiguration) -> bool {
        if self
            .is_datacenter_reachable(&datacenter_configuration.primary_datacenter_ip)
            .instrument(info_span!("contact datacenter primaire"))
            .await
        {
            return true;
        }

        if self
            .is_datacenter_reachable(&datacenter_configuration.secondary_datacenter_ip)
            .instrument(info_span!("contact datacenter secondaire"))
            .await
        {
            return true;
        }

        return false;
    }

    #[tracing::instrument(skip(self))]
    async fn is_datacenter_reachable(&self, ip: &Ipv4Addr) -> bool {
        let mut successive_ping_failures: u8 = 0;
        while successive_ping_failures < self.conf.ping_failures_limit_before_modem_reset {
            if self.pinger.is_ip_reachable(ip.clone()).await {
                info!("datacenter joignable");
                return true;
            }
            successive_ping_failures += 1;
            warn!(
                "échec du ping ({}/{})",
                successive_ping_failures, self.conf.ping_failures_limit_before_modem_reset
            );
            self.wait_until_next_try(successive_ping_failures).await;
        }

        warn!("datacenter injoignable");
        return false;
    }

    async fn wait_until_next_try(&self, successive_ping_failures: u8) {
        if successive_ping_failures >= self.conf.ping_failures_limit_before_modem_reset {
            warn!("limite atteinte");
            return;
        }

        let time_to_wait_before_next_ping = self.time_to_wait_before_next_ping();
        warn!(
            "echec du ping, attente de {}ms",
            time_to_wait_before_next_ping.as_millis()
        );
        sleep(time_to_wait_before_next_ping).await;
    }

    fn time_to_wait_before_next_ping(&self) -> Duration {
        let random_milliseconds: u64 = self
            .random_generator
            .between(0..self.conf.maximum_random_ping_delay.as_millis() as u64);

        self.conf
            .ping_interval
            .add(Duration::from_millis(random_milliseconds))
    }

    async fn restart_modem(&self) -> Result<(), NetworkError> {
        self.hal.reboot_modem().await
    }
}
