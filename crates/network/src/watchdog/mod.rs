use std::os::unix::net::{SocketAddr, UnixDatagram};
use std::sync::Arc;
use std::{env, time::Duration};
use tokio::sync::RwLock;
use tokio::time::Instant;
use tracing::{debug, error, info, warn};

#[cfg(target_os = "linux")]
use std::os::linux::net::SocketAddrExt;

const NOTIFY_SOCKET_NAME: &str = "NOTIFY_SOCKET";
const READY_MESSAGE: &str = "READY=1";
const WATCHDOG_MESSAGE: &str = "WATCHDOG=1";

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct TaskId(pub String);

impl TaskId {
    pub fn new(name: &str) -> Self {
        Self(name.to_string())
    }
}

#[derive(Debug, <PERSON><PERSON>)]
pub struct TaskHealth {
    pub last_heartbeat: Instant,
    pub timeout: Duration,
    pub is_critical: bool,
}

impl TaskHealth {
    pub fn new(timeout: Duration, is_critical: bool) -> Self {
        Self {
            last_heartbeat: Instant::now(),
            timeout,
            is_critical,
        }
    }

    pub fn is_healthy(&self) -> bool {
        self.last_heartbeat.elapsed() < self.timeout
    }

    pub fn update_heartbeat(&mut self) {
        self.last_heartbeat = Instant::now();
    }
}

#[derive(Debug, Clone)]
pub struct TaskHealthMonitor {
    tasks: Arc<RwLock<std::collections::HashMap<TaskId, TaskHealth>>>,
}

impl TaskHealthMonitor {
    pub fn new() -> Self {
        Self {
            tasks: Arc::new(RwLock::new(std::collections::HashMap::new())),
        }
    }

    pub async fn register_task(&self, task_id: TaskId, timeout: Duration, is_critical: bool) {
        let mut tasks = self.tasks.write().await;
        tasks.insert(task_id.clone(), TaskHealth::new(timeout, is_critical));
        info!(
            "Registered task '{}' for health monitoring (critical: {}, timeout: {:?})",
            task_id.0, is_critical, timeout
        );
    }

    pub async fn heartbeat(&self, task_id: &TaskId) {
        let mut tasks = self.tasks.write().await;
        if let Some(task_health) = tasks.get_mut(task_id) {
            task_health.update_heartbeat();
            debug!("Updated heartbeat for task '{}'", task_id.0);
        } else {
            warn!(
                "Attempted to update heartbeat for unregistered task '{}'",
                task_id.0
            );
        }
    }

    pub async fn all_critical_tasks_healthy(&self) -> bool {
        let tasks = self.tasks.read().await;
        for (task_id, task_health) in tasks.iter() {
            if task_health.is_critical && !task_health.is_healthy() {
                warn!(
                    "Critical task '{}' is unhealthy (last heartbeat: {:?} ago)",
                    task_id.0,
                    task_health.last_heartbeat.elapsed()
                );
                return false;
            }
        }
        true
    }

    pub async fn get_health_status(&self) -> Vec<(TaskId, bool)> {
        let tasks = self.tasks.read().await;
        tasks
            .iter()
            .map(|(id, health)| (id.clone(), health.is_healthy()))
            .collect()
    }

    pub async fn unregister_task(&self, task_id: &TaskId) {
        let mut tasks = self.tasks.write().await;
        if tasks.remove(task_id).is_some() {
            info!("Unregistered task '{}' from health monitoring", task_id.0);
        }
    }
}

#[derive(Debug, Clone)]
pub struct SystemdWatchdog {
    interval: Duration,
    health_monitor: Option<TaskHealthMonitor>,
}

impl SystemdWatchdog {
    pub fn new(interval: Duration) -> Self {
        info!("Initializing systemd watchdog with default configuration");
        Self {
            interval,
            health_monitor: None,
        }
    }

    pub fn new_with_health_monitor(interval: Duration, health_monitor: TaskHealthMonitor) -> Self {
        info!("Initializing systemd watchdog with health monitoring enabled");
        Self {
            interval,
            health_monitor: Some(health_monitor),
        }
    }

    pub fn get_health_monitor(&self) -> Option<&TaskHealthMonitor> {
        self.health_monitor.as_ref()
    }

    pub async fn start(&self) {
        info!("Starting systemd watchdog service");

        // Send initial ready signal
        if let Err(e) = self.send_ready_signal() {
            warn!("Failed to send initial ready signal to systemd: {}", e);
        } else {
            info!("Successfully sent ready signal to systemd");
        }

        // Start heartbeat loop
        loop {
            let should_send_heartbeat = if let Some(health_monitor) = &self.health_monitor {
                // Only send heartbeat if all critical tasks are healthy
                let all_healthy = health_monitor.all_critical_tasks_healthy().await;
                if !all_healthy {
                    warn!("Skipping systemd heartbeat due to unhealthy critical tasks");
                    // Log health status for debugging
                    let health_status = health_monitor.get_health_status().await;
                    for (task_id, is_healthy) in health_status {
                        if !is_healthy {
                            warn!("Task '{}' is unhealthy", task_id.0);
                        }
                    }
                }
                all_healthy
            } else {
                // No health monitoring, always send heartbeat (legacy behavior)
                true
            };

            if should_send_heartbeat {
                debug!("Sending watchdog heartbeat");
                if let Err(e) = self.send_heartbeat() {
                    warn!("Failed to send watchdog heartbeat: {}", e);
                } else {
                    debug!("Watchdog heartbeat sent successfully");
                }
            }

            tokio::time::sleep(self.interval).await;
        }
    }

    fn send_heartbeat(&self) -> Result<(), std::io::Error> {
        self.send_notification(WATCHDOG_MESSAGE)
    }

    fn send_ready_signal(&self) -> Result<(), std::io::Error> {
        self.send_notification(READY_MESSAGE)
    }

    fn send_notification(&self, message: &str) -> Result<(), std::io::Error> {
        let socket_path = match env::var(NOTIFY_SOCKET_NAME) {
            Ok(path) => {
                debug!("Found systemd notification socket at: {}", path);
                path
            }
            Err(_) => {
                debug!(
                    "Systemd notification socket not available ({} not set)",
                    NOTIFY_SOCKET_NAME
                );
                return Ok(());
            }
        };

        let socket = UnixDatagram::unbound().map_err(|e| {
            error!("Failed to create Unix datagram socket: {}", e);
            e
        })?;

        let message_bytes = message.as_bytes();

        // Send message to socket (handle both abstract and file-based sockets)
        let result = {
            if socket_path.starts_with('@') {
                let abstract_name = &socket_path[1..]; // Remove the '@' prefix
                let addr = SocketAddr::from_abstract_name(abstract_name.as_bytes())?;
                socket.connect_addr(&addr)?;
                socket.send(message_bytes)
            } else {
                socket.send_to(message_bytes, &socket_path)
            }
        };

        result.map_err(|e| {
            error!(
                "Failed to send notification to systemd socket '{}': {}",
                socket_path, e
            );
            e
        })?;

        debug!("Successfully sent notification to systemd");
        Ok(())
    }
}

#[cfg(test)]
mod tests;
