use std::time::Duration;

use tracing::info;

use crate::{
    configuration::NetworkConfiguration,
    connection::<PERSON><PERSON><PERSON><PERSON>,
    credentials::fetch_credentials,
    datacenter::ensure_primary_datacenter_is_detected,
    ports::{DnsResolverPort, HalPort, MsPort, ParamsPort, PingerPort, RandomGeneratorPort},
    retry::retry_async_until_success,
    watchdog::{TaskHealthMonitor, TaskId},
};

pub struct NetworkManager<
    HAL: Hal<PERSON>ort,
    MS: MsPort,
    PING: PingerPort,
    RANDOM: RandomGeneratorPort,
    DNS: DnsResolverPort,
    PARAMS: ParamsPort,
> {
    pub hal: HAL,
    pub configuration: NetworkConfiguration,
    pub module_securite: MS,
    pub pinger: PING,
    pub random_generator: RANDOM,
    pub dns_resolver: DNS,
    pub params: PARAMS,
    pub health_monitor: Option<TaskHealthMonitor>,
}

impl<
        HAL: HalPort,
        MS: MsPort,
        PING: PingerPort,
        RANDOM: RandomGeneratorPort,
        DNS: DnsResolverPort,
        PARAMS: ParamsPort,
    > NetworkManager<HAL, MS, PING, RAND<PERSON>, DNS, PARAMS>
{
    pub async fn run(&mut self) {
        info!("i2r-network en cours de fonctionnement");

        // Start background heartbeat task
        let heartbeat_handle = if let Some(health_monitor) = &self.health_monitor {
            let monitor = health_monitor.clone();
            let task_id = TaskId::new("network_manager");
            let interval = Duration::from(self.configuration.watchdog_interval_secs);

            Some(tokio::spawn(async move {
                let mut interval = tokio::time::interval(interval);
                loop {
                    interval.tick().await;
                    monitor.heartbeat(&task_id).await;
                }
            }))
        } else {
            None
        };

        // Initialization without manual heartbeats
        let credentials =
            fetch_credentials(&self.hal, &self.configuration, &self.module_securite).await;

        retry_async_until_success(
            || {
                self.hal
                    .connect(&credentials.apn, &credentials.login, &credentials.password)
            },
            Duration::from_secs(5),
        )
        .await;

        retry_async_until_success(
            || {
                ensure_primary_datacenter_is_detected(
                    &self.dns_resolver,
                    &self.params,
                    &self.configuration,
                )
            },
            Duration::from_secs(2),
        )
        .await;

        self.configuration = self.params.fetch_configuration();

        // Cancel background heartbeat since ConnectionHandler will take over
        if let Some(handle) = heartbeat_handle {
            handle.abort();
        }

        let connection_handler = ConnectionHandler::new(
            self.pinger.clone(),
            self.random_generator.clone(),
            self.hal.clone(),
            credentials.clone(),
            self.configuration.clone(),
            self.health_monitor.clone(),
        );

        connection_handler.watch().await;
    }
}
